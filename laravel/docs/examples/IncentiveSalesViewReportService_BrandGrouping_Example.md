# IncentiveSalesViewReportService Brand Grouping Feature

## Overview

The `IncentiveSalesViewReportService` now supports grouping products by brand, allowing you to aggregate sales data, targets, and incentive calculations at the brand level instead of individual product level.

## Usage Examples

### Method 1: Using the salesView method with groupByBrand parameter

```php
use App\Services\Reports\SalesIncentives\IncentiveSalesViewReportService;
use App\Services\Sales\SalesIncentiveHolder;

// Initialize the service
$salesIncentiveHolder = app(SalesIncentiveHolder::class);
$service = new IncentiveSalesViewReportService($salesIncentiveHolder);

// Configure the service
$service->setFrom($fromDate)
        ->setTo($toDate)
        ->setPerDivOrUserFilter($perDivOrUserFilter)
        ->setIsChecked($isBrickChecked)
        ->setYears($years)
        ->setMonths($months)
        ->setDivisionType($divisionType)
        ->setMappingType($mappingType);

// Get results grouped by brand
$groupedResults = $service->salesView($division, $line, $products, true); // true = group by brand

// Get individual product results (original behavior)
$individualResults = $service->salesView($division, $line, $products, false); // false = individual products
```

### Method 2: Manual grouping of existing results

```php
// Get individual product results first
$individualResults = $service->salesView($division, $line, $products);

// Then group them by brand
$groupedResults = $service->groupProductsByBrand($individualResults);
```

## Data Structure

### Individual Product Records (Original)
```php
[
    'id' => 1,
    'line' => 'Line A',
    'division' => 'Division X',
    'employee' => 'John Doe',
    'emp_code' => 'EMP001',
    'product' => 'Product A',
    'p_w' => 10.5,
    'product_value' => 100.0,
    'brand' => 'Brand X',
    'sales_unit' => 50,
    'sales_value' => 500.0,
    'target_unit' => 40,
    'target_value' => 400.0,
    'achievement_unit' => '125.00%',
    'achievement_value' => '125.00%',
    'color' => '#FF0000',
    'level' => 1,
    'is_total' => false,
]
```

### Grouped Brand Records (New)
```php
[
    'id' => 1,
    'line' => 'Line A',
    'division' => 'Division X',
    'employee' => 'John Doe',
    'emp_code' => 'EMP001',
    'product' => 'Brand X',           // Brand name becomes product name
    'p_w' => 26.0,                   // Sum of all products in this brand
    'product_value' => 250.0,        // Sum of all product values
    'brand' => 'Brand X',
    'sales_unit' => 80,              // Sum of all sales units
    'sales_value' => 800.0,          // Sum of all sales values
    'target_unit' => 65,             // Sum of all target units
    'target_value' => 650.0,         // Sum of all target values
    'achievement_unit' => '123.08%', // Recalculated: (80/65)*100
    'achievement_value' => '123.08%', // Recalculated: (800/650)*100
    'color' => '#FF0000',
    'level' => 1,
    'is_total' => false,
    'product_count' => 2,            // NEW: Number of products grouped
]
```

## Key Features

### 1. Brand Handling
- Products with brands are grouped by brand name
- Products without brands are grouped under "No Brand" (product field shows "No Brand", brand field is empty)
- Each brand group shows the brand name in the `product` field

### 2. Data Aggregation
- **Numerical fields are summed**: `p_w`, `product_value`, `sales_unit`, `sales_value`, `target_unit`, `target_value`
- **Achievement percentages are recalculated** based on aggregated totals
- **Metadata is preserved** from the first product in each group: `id`, `line`, `division`, `employee`, `emp_code`, `color`, `level`

### 3. Additional Information
- `product_count`: Shows how many individual products were grouped together
- `is_total`: Remains `false` for brand groups, `true` for total records

### 4. Total Records
- Total records (where `is_total` = true) are preserved unchanged
- They appear alongside the brand groups in the final result

## Use Cases

1. **Executive Reporting**: View performance by brand instead of individual products
2. **Brand Analysis**: Compare performance across different brands
3. **Simplified Dashboards**: Reduce data complexity by grouping related products
4. **Brand Manager Reports**: Focus on brand-level metrics and incentives

## Backward Compatibility

The new functionality is completely backward compatible:
- Default behavior remains unchanged (individual products)
- Existing code continues to work without modification
- New parameter is optional with default value `false`
